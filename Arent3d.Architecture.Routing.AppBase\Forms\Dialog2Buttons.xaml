﻿<UserControl x:Class="Arent3d.Architecture.Routing.AppBase.Forms.Dialog2Buttons"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Arent3d.Architecture.Routing.AppBase.Forms"
             mc:Ignorable="d"
             d:DesignHeight="30"
             Width="Auto"
             x:Name="root">
    <Grid Width="Auto" Height="30">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="65" />
            <ColumnDefinition Width="65" />
        </Grid.ColumnDefinitions>

        <Button x:Name="Left" Content="{Binding LeftButton, ElementName=root}"
                Width="60" Height="30"
                Grid.Column="0"
                Margin="2,0,2,0"
                Click="Left_OnClick"
                IsEnabled="{Binding IsEnableLeftButton, ElementName=root}" />
        <Button x:Name="Right" Content="{Binding RightButton, ElementName=root}" 
                Width="60" Height="30"
                Grid.Column="1"
                Margin="2,0,2,0"
                Click="Right_OnClick" />
    </Grid>

</UserControl>