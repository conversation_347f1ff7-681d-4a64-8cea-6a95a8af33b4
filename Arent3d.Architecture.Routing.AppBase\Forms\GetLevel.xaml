﻿<Window x:Class="Arent3d.Architecture.Routing.AppBase.Forms.GetLevel"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        mc:Ignorable="d" Height="450" Width="320" Title="Select Levels" WindowStyle="ToolWindow"
        d:DesignHeight="450" d:DesignWidth="800">
    <DockPanel Margin="10">
        <ListBox ItemsSource="{Binding LevelList, RelativeSource={RelativeSource FindAncestor, AncestorType=Window}}" DockPanel.Dock="Top" Height="300">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <CheckBox IsChecked="{Binding IsSelected}" Content="{Binding LevelName}" />
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
        <StackPanel DockPanel.Dock="Bottom">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Button Height="24"
                        Grid.Column="0" Grid.Row="0"
                        Content="Check" Click="CheckAll"
                        Margin="0,10,3,0" />
                <Button Height="24"
                        Grid.Column="1" Grid.Row="0"
                        Content="Uncheck" Click="UncheckAll"
                        Margin="3,10,3,0" />
                <Button Height="24"
                        Grid.Column="2" Grid.Row="0"
                        Content="Toggle" Click="ToggleAll"
                        Margin="3,10,0,0" />
            </Grid>
            <Button Content="Select" Height="32" Click="SelectButton_Click" Margin="0,10,0,0" />
        </StackPanel>
    </DockPanel>
</Window>