﻿<Window x:Class="Arent3d.Architecture.Routing.AppBase.Forms.SelectEndPoint"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        mc:Ignorable="d" Height="400" Width="300" WindowStyle="ToolWindow"
        d:DesignHeight="450" d:DesignWidth="300">
    <DockPanel Margin="10">
        <ListBox ItemsSource="{Binding EndPointList, RelativeSource={RelativeSource FindAncestor, AncestorType=Window}}" DockPanel.Dock="Top" Height="300">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <RadioButton
                        GroupName="EndPoint"
                        IsChecked="{Binding IsSelected}"
                        Content="{Binding}"/>
                </DataTemplate>
            </ListBox.ItemTemplate>
            <ListBox.ItemContainerStyle>
                <Style TargetType="{x:Type ListBoxItem}">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                </Style>
            </ListBox.ItemContainerStyle>
        </ListBox>
        <StackPanel DockPanel.Dock="Bottom">
            <Button Content="Select" Height="32" Click="Button_Click" Margin="0,10,0,0" />
        </StackPanel>
    </DockPanel>
</Window>