﻿<UserControl x:Class="Arent3d.Architecture.Routing.Mechanical.App.Forms.SimpleFromToEditControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:Arent3d.Architecture.Routing.Mechanical.App.Forms"
             xmlns:valueConverters="clr-namespace:Arent3d.Architecture.Routing.AppBase.Forms.ValueConverters"
             xmlns:forms="clr-namespace:Arent3d.Architecture.Routing.AppBase.Forms;assembly=Arent3d.Architecture.Routing.AppBase"
             xmlns:valueConverters1="clr-namespace:Arent3d.Architecture.Routing.AppBase.Forms.ValueConverters;assembly=Arent3d.Architecture.Routing.AppBase"
             mc:Ignorable="d"
             Background="White"
             Height="Auto" Width="Auto">
    <UserControl.Resources>
        <Style TargetType="{x:Type Label}" x:Key="LengthUnit">
            <Setter Property="Content" Value="&quot;"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding DisplayUnitSystem, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="IMPERIAL">
                    <Setter Property="Content" Value="&quot;"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DisplayUnitSystem, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="METRIC">
                    <Setter Property="Content" Value="mm"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Border BorderBrush="LightGray" BorderThickness="1">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <Label Content="System Type"
                   HorizontalAlignment="Left"
                   VerticalAlignment="Center"
                   Grid.Column="0"
                   Grid.Row="0">
                <Label.Style>
                    <Style TargetType="{x:Type Label}">
                        <Setter Property="Foreground" Value="{DynamicResource SystemColors.ControlTextBrushKey}"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding UseSystemType, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="False">
                                <Setter Property="Foreground" Value="{DynamicResource SystemColors.GrayTextBrushKey}"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Label.Style>
            </Label>
            <ComboBox Grid.Column="1"
                      Grid.Row="0"
                      ItemsSource="{Binding  SystemTypes, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                      DisplayMemberPath="Name"
                      SelectionChanged="SystemTypeComboBox_SelectionChanged"
                      SelectedIndex="{Binding SystemTypeIndex, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                      VerticalAlignment="Center">
                <ComboBox.Style>
                    <Style TargetType="{x:Type ComboBox}">
                        <Setter Property="IsEnabled" Value="True"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding SystemTypeEditable, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="False">
                                <Setter Property="IsEnabled" Value="False"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding UseSystemType, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="False">
                                <Setter Property="IsEnabled" Value="False"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ComboBox.Style>
            </ComboBox>
            
            <Label Content="Diameter"
                   HorizontalAlignment="Left"
                   VerticalAlignment="Center"
                   Grid.Column="0"
                   Grid.Row="2" />
            <ComboBox Grid.Column="1"
                      Grid.Row="2"
                      ItemsSource="{Binding Diameters, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                      SelectionChanged="DiameterComboBox_SelectionChanged"
                      SelectedIndex="{Binding DiameterIndex, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                      VerticalAlignment="Center">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock>
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="Text" Value="{Binding Converter={x:Static valueConverters1:LengthToStringConverter.Default}, Mode=OneWay}"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding DisplayUnitSystem, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="IMPERIAL">
                                            <Setter Property="Text" Value="{Binding Converter={x:Static valueConverters1:LengthToStringConverter.Inches}, Mode=OneWay}"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding DisplayUnitSystem, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="METRIC">
                                            <Setter Property="Text" Value="{Binding Converter={x:Static valueConverters1:LengthToStringConverter.Millimeters}, Mode=OneWay}"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>

            <Label Grid.Row="4" Grid.Column="0" Content="First connector through height"
                   HorizontalAlignment="Left"
                   VerticalAlignment="Center" />
            <StackPanel Grid.Row="4" Grid.Column="1" VerticalAlignment="Center"
                        Orientation="Horizontal">
                <CheckBox x:Name="FromHeightSetting" Margin="0,0,0,0"
                          IsChecked="{Binding UseFromFixedHeight, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                          Checked="Height_OnChecked"
                          Unchecked="Height_OnUnchecked"
                          HorizontalAlignment="Left"
                          VerticalAlignment="Center" />
                <ComboBox Name="FromLocationTypeComboBox"
                          ItemsSource="{Binding LocationTypes, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                          SelectionChanged="LocationTypeComboBox_SelectionChanged"
                          SelectedIndex="{Binding FromLocationTypeIndex, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                          VerticalAlignment="Center"
                          DisplayMemberPath="Value"
                          Margin="5,0,5,0"
                          Padding="5,0,0,4">
                    <ComboBox.Style>
                        <Style TargetType="{x:Type ComboBox}">
                            <Setter Property="Visibility" Value="Hidden"/>
                            <Setter Property="Height" Value="18"/>
                            <Setter Property="FontSize" Value="12"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsChecked, ElementName=FromHeightSetting}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ComboBox.Style>
                </ComboBox>
                <!-- Manually update Value because binding is not called -->
                <forms:NumericUpDownEx x:Name="FromFixedHeightNumericUpDown"
                                       ValueChanged="FromFixedHeightNumericUpDown_OnValueChanged"
                                       TextAlignment="Right"
                                       Increment="100"
                                       Width="80">
                    <forms:NumericUpDownEx.Style>
                        <Style TargetType="{x:Type forms:NumericUpDownEx}">
                            <Setter Property="Visibility" Value="Hidden"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsChecked, ElementName=FromHeightSetting}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </forms:NumericUpDownEx.Style>
                </forms:NumericUpDownEx>
                <Label Margin="0,0,0,0">
                    <Label.Style>
                        <Style TargetType="{x:Type Label}" BasedOn="{StaticResource LengthUnit}">
                            <Setter Property="Visibility" Value="Hidden"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsChecked, ElementName=FromHeightSetting}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Label.Style>
                </Label>
            </StackPanel>

            <Label Grid.Row="5" Grid.Column="0" Content="Second connector through height"
                   HorizontalAlignment="Left"
                   VerticalAlignment="Center">
                <Label.Style>
                    <Style TargetType="Label">
                        <Setter Property="Visibility" Value="Hidden"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsDifferentLevel, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Label.Style>
            </Label>
            <StackPanel Grid.Row="5" Grid.Column="1" VerticalAlignment="Center"
                        Orientation="Horizontal">
                <StackPanel.Style>
                    <Style TargetType="StackPanel">
                        <Setter Property="Visibility" Value="Hidden"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsDifferentLevel, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </StackPanel.Style>

                <CheckBox x:Name="ToHeightSetting" Margin="0,0,0,0"
                          IsChecked="{Binding UseToFixedHeight, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                          Checked="ToHeight_OnChecked"
                          Unchecked="ToHeight_OnUnchecked"
                          HorizontalAlignment="Left"
                          VerticalAlignment="Center"/>
                <ComboBox Name="ToLocationTypeComboBox"
                          ItemsSource="{Binding LocationTypes, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                          SelectionChanged="LocationTypeComboBox_SelectionChanged"
                          SelectedIndex="{Binding ToLocationTypeIndex, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                          VerticalAlignment="Center"
                          DisplayMemberPath="Value"
                          Margin="5,0,5,0"
                          Padding="5,0,0,4">
                    <ComboBox.Style>
                        <Style TargetType="{x:Type ComboBox}">
                            <Setter Property="Visibility" Value="Hidden"/>
                            <Setter Property="Height" Value="18"/>
                            <Setter Property="FontSize" Value="12"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsChecked, ElementName=ToHeightSetting}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ComboBox.Style>
                </ComboBox>
                <!-- Manually update Value because binding is not called -->
                <forms:NumericUpDownEx x:Name="ToFixedHeightNumericUpDown"
                                       ValueChanged="ToFixedHeightNumericUpDown_OnValueChanged"
                                       TextAlignment="Right"
                                       Increment="100"
                                       Width="80">
                    <forms:NumericUpDownEx.Style>
                        <Style TargetType="{x:Type forms:NumericUpDownEx}">
                            <Setter Property="Visibility" Value="Hidden"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsChecked, ElementName=ToHeightSetting}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </forms:NumericUpDownEx.Style>
                </forms:NumericUpDownEx>
                <Label Margin="0,0,0,0">
                    <Label.Style>
                        <Style TargetType="{x:Type Label}" BasedOn="{StaticResource LengthUnit}">
                            <Setter Property="Visibility" Value="Hidden"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsChecked, ElementName=ToHeightSetting}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Label.Style>
                </Label>
            </StackPanel>
            
            <Label Content="Avoid Type"
                   HorizontalAlignment="Left"
                   VerticalAlignment="Center"
                   Grid.Column="0"
                   Grid.Row="6" />

            <ComboBox Grid.Column="1"
                      Grid.Row="6"
                      ItemsSource="{Binding AvoidTypes, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                      DisplayMemberPath="Value"
                      SelectedIndex="{Binding AvoidTypeIndex, RelativeSource={RelativeSource FindAncestor, AncestorType=local:SimpleFromToEditControl}}"
                      SelectionChanged="AvoidTypeComboBox_OnSelectionChanged"
                      VerticalAlignment="Center">
            </ComboBox>
            
        </Grid>
    </Border>
</UserControl>