using System ;
using System.Collections.Generic ;
using System.Linq ;
using Arent3d.Architecture.Routing.AppBase ;
using Arent3d.Architecture.Routing.AppBase.Commands.Routing ;
using Arent3d.Architecture.Routing.AppBase.Forms ;
using Arent3d.Architecture.Routing.EndPoints ;
using Arent3d.Architecture.Routing.StorableCaches ;
using Arent3d.Revit ;
using Arent3d.Revit.I18n ;
using Arent3d.Revit.UI ;
using Autodesk.Revit.Attributes ;
using Autodesk.Revit.DB ;
using Autodesk.Revit.UI ;
using Autodesk.Revit.UI.Selection ;

namespace Arent3d.Architecture.Routing.Mechanical.App.Commands.Routing
{
  [Transaction( TransactionMode.Manual )]
  [DisplayNameKey( "Mechanical.App.Commands.Routing.AutoRoutingSupplyAirGrillCommand", DefaultString = "Auto Routing\nSupply Air Grill" )]
  [Image( "resources/RerouteAll.png" )]
  public class AutoRoutingSupplyAirGrillCommand : RoutingCommandBase<AutoRoutingSupplyAirGrillCommand.AutoRoutingState>
  {
    private const double DefaultSearchRadius = 10.0 ; // Default search radius in feet

    protected override string GetTransactionNameKey() => "TransactionName.Commands.Routing.AutoRoutingSupplyAirGrill" ;

    public record AutoRoutingState( Element AirBoxElement, IReadOnlyCollection<Connector> AirBoxConnectors, IReadOnlyCollection<Element> AirGrills, IRouteProperty RouteProperty, MEPSystemClassificationInfo ClassificationInfo ) ;

    protected override RoutingExecutor CreateRoutingExecutor( Document document, View view ) => AppCommandSettings.CreateRoutingExecutor( document, view ) ;

    protected override OperationResult<AutoRoutingState> OperateUI( ExternalCommandData commandData, ElementSet elements )
    {
      var uiDocument = commandData.Application.ActiveUIDocument ;
      var document = uiDocument.Document ;

      try {
        // Step 1: Select AirBox element
        var airBoxElement = SelectAirBoxElement( uiDocument ) ;
        if ( null == airBoxElement ) return OperationResult<AutoRoutingState>.Cancelled ;

        // Step 2: Get connectors from AirBox
        var airBoxConnectors = GetAirBoxConnectors( airBoxElement ) ;
        if ( false == airBoxConnectors.Any() ) {
          TaskDialog.Show( "Error", "Selected AirBox element has no connectors." ) ;
          return OperationResult<AutoRoutingState>.Failed ;
        }

        // Step 3: Find air grills within radius
        var airGrills = FindAirGrillsNearAirBox( document, airBoxElement, DefaultSearchRadius ) ;
        if ( false == airGrills.Any() ) {
          TaskDialog.Show( "Error", "No air grills found within search radius." ) ;
          return OperationResult<AutoRoutingState>.Failed ;
        }

        // Step 4: Create default route properties
        var routeProperty = CreateDefaultRouteProperty( document, airBoxConnectors.First() ) ;
        if ( null == routeProperty ) {
          TaskDialog.Show( "Error", "Could not determine routing properties." ) ;
          return OperationResult<AutoRoutingState>.Failed ;
        }

        // Step 5: Get classification info
        var classificationInfo = GetClassificationInfo( airBoxConnectors.First() ) ;
        if ( null == classificationInfo ) {
          TaskDialog.Show( "Error", "Could not determine system classification." ) ;
          return OperationResult<AutoRoutingState>.Failed ;
        }

        return new OperationResult<AutoRoutingState>( new AutoRoutingState( airBoxElement, airBoxConnectors, airGrills, routeProperty, classificationInfo ) ) ;
      }
      catch ( OperationCanceledException ) {
        return OperationResult<AutoRoutingState>.Cancelled ;
      }
      catch ( Exception ex ) {
        TaskDialog.Show( "Error", $"An error occurred: {ex.Message}" ) ;
        return OperationResult<AutoRoutingState>.Failed ;
      }
    }

    protected override IReadOnlyCollection<(string RouteName, RouteSegment Segment)> GetRouteSegments( Document document, AutoRoutingState state )
    {
      var segments = new List<(string RouteName, RouteSegment Segment)>() ;
      var routes = RouteCache.Get( document ) ;

      // Create routing segments between each AirBox connector and nearest available air grill
      var availableAirGrills = state.AirGrills.ToList() ;

      foreach ( var connector in state.AirBoxConnectors ) {
        if ( false == availableAirGrills.Any() ) break ; // No more air grills available

        // Find nearest air grill to this connector
        var nearestAirGrill = FindNearestAirGrill( connector, availableAirGrills ) ;
        if ( null == nearestAirGrill ) continue ;

        // Remove from available list to avoid duplicate assignments
        availableAirGrills.Remove( nearestAirGrill ) ;

        // Create route segment
        var segment = CreateRouteSegment( document, connector, nearestAirGrill, state.RouteProperty, state.ClassificationInfo ) ;
        if ( null != segment ) {
          var routeName = GenerateRouteName( routes, state.RouteProperty ) ;
          routes.FindOrCreate( routeName ) ;
          segments.Add( ( routeName, segment ) ) ;
        }
      }

      return segments ;
    }

    private Element? SelectAirBoxElement( UIDocument uiDocument )
    {
      var filter = new AirBoxSelectionFilter() ;
      var message = "Select an AirBox element" ;

      try {
        var pickedObject = uiDocument.Selection.PickObject( ObjectType.Element, filter, message ) ;
        return uiDocument.Document.GetElement( pickedObject.ElementId ) ;
      }
      catch ( OperationCanceledException ) {
        return null ;
      }
    }

    private IReadOnlyCollection<Connector> GetAirBoxConnectors( Element airBoxElement )
    {
      var connectors = new List<Connector>() ;

      if ( airBoxElement is FamilyInstance familyInstance ) {
        var connectorManager = familyInstance.MEPModel?.ConnectorManager ;
        if ( null != connectorManager ) {
          foreach ( Connector connector in connectorManager.Connectors ) {
            if ( connector.Domain == Domain.DomainHvac && connector.ConnectorType == ConnectorType.AnyEnd ) {
              connectors.Add( connector ) ;
            }
          }
        }
      }

      return connectors ;
    }

    private IReadOnlyCollection<Element> FindAirGrillsNearAirBox( Document document, Element airBoxElement, double searchRadius )
    {
      var airGrills = new List<Element>() ;
      var airBoxLocation = GetElementLocation( airBoxElement ) ;
      if ( null == airBoxLocation ) return airGrills ;

      // Create bounding box filter for performance
      var searchBox = new BoundingBoxXYZ
      {
        Min = airBoxLocation - new XYZ( searchRadius, searchRadius, searchRadius ),
        Max = airBoxLocation + new XYZ( searchRadius, searchRadius, searchRadius )
      } ;
      var boxFilter = new BoundingBoxIntersectsFilter( new Outline( searchBox.Min, searchBox.Max ) ) ;

      // Filter for air terminals (air grills)
      var categoryFilter = new ElementCategoryFilter( BuiltInCategory.OST_DuctTerminal ) ;
      var combinedFilter = new LogicalAndFilter( boxFilter, categoryFilter ) ;

      var collector = new FilteredElementCollector( document ) ;
      var elements = collector.WherePasses( combinedFilter ).WhereElementIsNotElementType().ToElements() ;

      foreach ( var element in elements ) {
        var elementLocation = GetElementLocation( element ) ;
        if ( null != elementLocation ) {
          var distance = airBoxLocation.DistanceTo( elementLocation ) ;
          if ( distance <= searchRadius && HasCompatibleConnectors( element ) ) {
            airGrills.Add( element ) ;
          }
        }
      }

      return airGrills ;
    }

    private XYZ? GetElementLocation( Element element )
    {
      return element.Location switch
      {
        LocationPoint locationPoint => locationPoint.Point,
        LocationCurve locationCurve => locationCurve.Curve.Evaluate( 0.5, true ),
        _ => null
      } ;
    }

    private bool HasCompatibleConnectors( Element element )
    {
      if ( element is not FamilyInstance familyInstance ) return false ;

      var connectorManager = familyInstance.MEPModel?.ConnectorManager ;
      if ( null == connectorManager ) return false ;

      foreach ( Connector connector in connectorManager.Connectors ) {
        if ( connector.Domain == Domain.DomainHvac && connector.ConnectorType == ConnectorType.AnyEnd ) {
          return true ;
        }
      }

      return false ;
    }

    private Element? FindNearestAirGrill( Connector airBoxConnector, IReadOnlyCollection<Element> availableAirGrills )
    {
      Element? nearestAirGrill = null ;
      var minDistance = double.MaxValue ;
      var connectorLocation = airBoxConnector.Origin ;

      foreach ( var airGrill in availableAirGrills ) {
        var airGrillLocation = GetElementLocation( airGrill ) ;
        if ( null == airGrillLocation ) continue ;

        var distance = connectorLocation.DistanceTo( airGrillLocation ) ;
        if ( distance < minDistance ) {
          minDistance = distance ;
          nearestAirGrill = airGrill ;
        }
      }

      return nearestAirGrill ;
    }

    private RouteSegment? CreateRouteSegment( Document document, Connector airBoxConnector, Element airGrill, IRouteProperty routeProperty, MEPSystemClassificationInfo classificationInfo )
    {
      try {
        // Create from endpoint (AirBox connector)
        var fromEndPoint = new ConnectorEndPoint( airBoxConnector, double.Epsilon ) ;

        // Create to endpoint (Air grill)
        var toEndPoint = CreateAirGrillEndPoint( airGrill ) ;
        if ( null == toEndPoint ) return null ;

        // Create route segment
        var systemType = routeProperty.GetSystemType() ;
        var curveType = routeProperty.GetCurveType() ;
        var diameter = routeProperty.GetDiameter() ;
        var fromFixedHeight = routeProperty.GetFromFixedHeight() ;
        var toFixedHeight = routeProperty.GetToFixedHeight() ;
        var avoidType = routeProperty.GetAvoidType() ;
        var shaft = routeProperty.GetShaft() ;
        var shaftElementId = ElementId.InvalidElementId ;

        return new RouteSegment( classificationInfo, systemType, curveType, fromEndPoint, toEndPoint, diameter, routeProperty.GetRouteOnPipeSpace(), fromFixedHeight, toFixedHeight, avoidType, shaftElementId ) ;
      }
      catch ( Exception ) {
        return null ;
      }
    }

    private IEndPoint? CreateAirGrillEndPoint( Element airGrill )
    {
      if ( airGrill is not FamilyInstance familyInstance ) return null ;

      var connectorManager = familyInstance.MEPModel?.ConnectorManager ;
      if ( null == connectorManager ) return null ;

      // Find the first compatible connector
      foreach ( Connector connector in connectorManager.Connectors ) {
        if ( connector.Domain == Domain.DomainHvac && connector.ConnectorType == ConnectorType.Duct ) {
          return new ConnectorEndPoint( connector ) ;
        }
      }

      return null ;
    }

    private IRouteProperty? CreateDefaultRouteProperty( Document document, Connector referenceConnector )
    {
      try {
        // Get classification info from connector
        var classificationInfo = MEPSystemClassificationInfo.From( referenceConnector ) ;
        if ( null == classificationInfo ) return null ;

        // Get system type
        var systemType = RouteMEPSystem.GetSystemType( document, referenceConnector ) ;

        // Get curve type
        var curveType = RouteMEPSystem.GetMEPCurveType( document, new[] { referenceConnector }, systemType ) ;

        // Get diameter
        var diameter = referenceConnector.GetDiameter() ;

        // Create route property type list for default values
        var fromLevelId = document.GuessLevel( referenceConnector.Origin ).Id ;
        var toLevelId = fromLevelId ; // Same level for air grills typically
        var routeChoiceSpec = new RoutePropertyTypeList( document, classificationInfo, fromLevelId, toLevelId ) ;

        // Create default route properties
        return new RouteProperties( document, classificationInfo, systemType, curveType, routeChoiceSpec.StandardTypes?.FirstOrDefault(), diameter ) ;
      }
      catch ( Exception ) {
        return null ;
      }
    }

    private MEPSystemClassificationInfo? GetClassificationInfo( Connector connector )
    {
      return MEPSystemClassificationInfo.From( connector ) ;
    }

    private string GenerateRouteName( RouteCache routes, IRouteProperty routeProperty )
    {
      var systemType = routeProperty.GetSystemType() ;
      var curveType = routeProperty.GetCurveType() ;

      var nameBase = GetNameBase( systemType, curveType ) ;
      var nextIndex = GetRouteNameIndex( routes, nameBase ) ;

      return nameBase + "_" + nextIndex ;
    }

    private string GetNameBase( MEPSystemType? systemType, MEPCurveType curveType )
    {
      var systemTypeName = systemType?.Name ?? "Unknown" ;
      var curveTypeName = curveType.Name ;

      return $"AutoRoute_{systemTypeName}_{curveTypeName}" ;
    }

    private int GetRouteNameIndex( RouteCache routes, string nameBase )
    {
      var existingRoutes = routes.Values.Where( r => r.Name.StartsWith( nameBase + "_" ) ) ;
      var maxIndex = 0 ;

      foreach ( var route in existingRoutes ) {
        var parts = route.Name.Split( '_' ) ;
        if ( parts.Length > 0 && int.TryParse( parts.Last(), out var index ) ) {
          maxIndex = Math.Max( maxIndex, index ) ;
        }
      }

      return maxIndex + 1 ;
    }
  }

  /// <summary>
  /// Selection filter for AirBox elements (typically mechanical equipment with HVAC connectors)
  /// </summary>
  public class AirBoxSelectionFilter : ISelectionFilter
  {
    public bool AllowElement( Element elem )
    {
      // Allow mechanical equipment that has HVAC connectors
      if ( elem.GetBuiltInCategory() != BuiltInCategory.OST_MechanicalEquipment ) return false ;
      if ( elem is not FamilyInstance familyInstance ) return false ;

      // Check if element has HVAC connectors
      var connectorManager = familyInstance.MEPModel?.ConnectorManager ;
      if ( null == connectorManager ) return false ;

      return connectorManager.Connectors.Cast<Connector>().Any( connector => connector.Domain == Domain.DomainHvac && connector.ConnectorType == ConnectorType.AnyEnd ) ;
    }

    public bool AllowReference( Reference reference, XYZ position ) => false ;
  }
}