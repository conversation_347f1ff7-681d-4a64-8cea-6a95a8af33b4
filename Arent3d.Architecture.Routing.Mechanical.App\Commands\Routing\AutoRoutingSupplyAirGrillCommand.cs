using System.Collections.Generic ;
using Arent3d.Architecture.Routing.AppBase ;
using Arent3d.Architecture.Routing.AppBase.Commands.Routing ;
using Arent3d.Architecture.Routing.EndPoints ;
using Arent3d.Revit.UI ;
using Autodesk.Revit.Attributes ;
using Autodesk.Revit.DB ;
using Autodesk.Revit.UI ;

namespace Arent3d.Architecture.Routing.Mechanical.App.Commands.Routing
{
  [Transaction( TransactionMode.Manual )]
  [DisplayNameKey( "Mechanical.App.Commands.Routing.AutoRoutingSupplyAirGrillCommand", DefaultString = "Auto Routing\n Supply Air Grill" )]
  [Image( "resources/RerouteAll.png" )]
  public class AutoRoutingSupplyAirGrillCommand : RoutingCommandBase<AutoRoutingSupplyAirGrillCommand.PickState>
  {
    protected override string GetTransactionNameKey() => "TransactionName.Commands.Routing.AutoRoutingSupplyAirGrill" ;
    
    public record PickState( ICollection<ConnectorPicker.IPickResult> FromPickResult, ICollection<ConnectorPicker.IPickResult> ToPickResult, IRouteProperty PropertyDialog, MEPSystemClassificationInfo ClassificationInfo ) ;
    protected override RoutingExecutor CreateRoutingExecutor( Document document, View view ) => AppCommandSettings.CreateRoutingExecutor( document, view ) ;

    protected override IReadOnlyCollection<(string RouteName, RouteSegment Segment)> GetRouteSegments( Document document, PickState state )
    {
      throw new System.NotImplementedException() ;
    }
    
    // protected override DialogInitValues? CreateSegmentDialogDefaultValuesWithConnector( Document document, Connector connector,
    //   MEPSystemClassificationInfo classificationInfo )
    // {
    //   if ( RouteMEPSystem.GetSystemType( document, connector ) is not { } defaultSystemType ) return null ;
    //
    //   var curveType = RouteMEPSystem.GetMEPCurveType( document, new[] { connector }, defaultSystemType ) ;
    //
    //   return new DialogInitValues( classificationInfo, defaultSystemType, curveType, connector.GetDiameter() ) ;
    // }

    protected override OperationResult<PickState> OperateUI( ExternalCommandData commandData, ElementSet elements )
    {
      throw new System.NotImplementedException() ;
    }
  }
}